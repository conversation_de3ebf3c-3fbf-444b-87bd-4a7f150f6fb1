/**
 * Setup Wizard Styles
 * Includes essential WordPress admin styles to avoid loading deprecated functions
 */

/* Essential WordPress admin button styles */
.button,
.button-primary,
.button-secondary {
  display: inline-block;
  text-decoration: none;
  font-size: 13px;
  line-height: 2.15384615;
  min-height: 30px;
  margin: 0;
  padding: 0 10px;
  cursor: pointer;
  border-width: 1px;
  border-style: solid;
  -webkit-appearance: none;
  border-radius: 3px;
  white-space: nowrap;
  box-sizing: border-box;
}

.button-primary {
  background: #0073aa;
  border-color: #0073aa;
  color: #fff;
}

.button-secondary {
  background: #f7f7f7;
  border-color: #ccc;
  color: #555;
}

.button:hover,
.button:focus {
  background: #f3f5f6;
  border-color: #999;
  color: #23282d;
}

.button-primary:hover,
.button-primary:focus {
  background: #005a87;
  border-color: #005a87;
  color: #fff;
}

/* Essential form styles */
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
select,
textarea {
  background-color: #fff;
  border: 1px solid #ddd;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.07);
  color: #32373c;
  outline: 0;
  transition: 0.05s border-color ease-in-out;
}

input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="range"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
input[type="time"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="color"]:focus,
select:focus,
textarea:focus {
  border-color: #5b9dd9;
  box-shadow: 0 0 2px rgba(30, 140, 190, 0.8);
}

/* Essential dashicons */
@font-face {
  font-family: "dashicons";
  src: url("../../../wp-includes/fonts/dashicons.woff2") format("woff2"),
    url("../../../wp-includes/fonts/dashicons.woff") format("woff"),
    url("../../../wp-includes/fonts/dashicons.ttf") format("truetype"),
    url("../../../wp-includes/fonts/dashicons.svg#dashicons") format("svg");
  font-weight: normal;
  font-style: normal;
}

.dashicons,
.dashicons-before:before {
  font-family: "dashicons";
  display: inline-block;
  line-height: 1;
  font-weight: 400;
  font-style: normal;
  speak: none;
  text-decoration: inherit;
  text-transform: none;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  width: 20px;
  height: 20px;
  font-size: 20px;
  vertical-align: top;
  text-align: center;
  transition: color 0.1s ease-in;
}

.pbc-setup {
  background: linear-gradient(135deg, #f1f1f1 0%, #e8e8e8 100%);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
  min-height: 100vh;
  margin: 0;
  padding: 20px;
}

.pbc-setup-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
}

.pbc-setup *,
.pbc-setup *::before,
.pbc-setup *::after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

#pbc-logo {
  border: 0;
  margin: 0 0 32px 0;
  padding: 0;
  text-align: center;
}

#pbc-logo a {
  background: linear-gradient(135deg, #96588a 0%, #a16696 100%);
  color: #fff;
  text-decoration: none;
  display: inline-block;
  padding: 1.2em 2.5em;
  font-weight: 600;
  font-size: 20px;
  line-height: 1;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(150, 88, 138, 0.3);
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

#pbc-logo a:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(150, 88, 138, 0.4);
  background: linear-gradient(135deg, #a16696 0%, #96588a 100%);
}

.pbc-setup-steps {
  list-style: none outside;
  overflow: hidden;
  margin: 0 0 24px 0;
  padding: 0;
  display: table;
  width: 100%;
  table-layout: fixed;
  border-spacing: 0;
  border-collapse: separate;
}

.pbc-setup-steps li {
  display: table-cell;
  width: 25%;
  float: none;
  padding: 0;
  margin: 0;
  text-align: center;
  position: relative;
  background-color: #fff;
  color: #6c757d;
  border: 3px solid #e9ecef;
  border-right-width: 0;
  line-height: 1.4em;
  transition: all 0.3s ease;
}

.pbc-setup-steps li::before {
  content: "";
  border: 4px solid #f1f1f1;
  border-left-color: #fff;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  top: -4px;
  right: -8px;
  z-index: 2;
  width: 0;
  height: 0;
  border-width: 27px 0 27px 27px;
}

.pbc-setup-steps li::after {
  content: "";
  border: 4px solid #f1f1f1;
  border-left-color: #f1f1f1;
  border-right-color: transparent;
  border-top-color: transparent;
  border-bottom-color: transparent;
  position: absolute;
  top: -4px;
  right: -12px;
  z-index: 1;
  width: 0;
  height: 0;
  border-width: 27px 0 27px 27px;
}

.pbc-setup-steps li:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.pbc-setup-steps li:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
  border-right-width: 4px;
}

.pbc-setup-steps li:last-child::before,
.pbc-setup-steps li:last-child::after {
  display: none;
}

.pbc-setup-steps li a {
  display: inline-block;
  padding: 1em;
  text-decoration: none;
  color: inherit;
  width: 100%;
}

.pbc-setup-steps .active {
  background: linear-gradient(135deg, #96588a 0%, #a16696 100%);
  color: #fff;
  border-color: #96588a;
  box-shadow: 0 4px 15px rgba(150, 88, 138, 0.3);
  transform: translateY(-2px);
}

.pbc-setup-steps .active::before {
  border-left-color: #96588a;
}

.pbc-setup-steps .done {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: #fff;
  border-color: #28a745;
  box-shadow: 0 2px 10px rgba(40, 167, 69, 0.3);
}

.pbc-setup-steps .done::before {
  border-left-color: #28a745;
}

.pbc-setup-steps li:hover:not(.active):not(.done) {
  background: #f8f9fa;
  border-color: #dee2e6;
  transform: translateY(-1px);
}

.pbc-setup-content {
  background: #fff;
  overflow: hidden;
  padding: 2.5em;
  margin: 0 0 32px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(150, 88, 138, 0.1);
  position: relative;
}

.pbc-setup-content::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #96588a 0%, #a16696 100%);
  border-radius: 12px 12px 0 0;
}

.pbc-setup-content h1,
.pbc-setup-content h2,
.pbc-setup-content h3 {
  line-height: 1.4em;
  margin: 0 0 1em 0;
  color: #666;
}

.pbc-setup-content h1 {
  font-size: 2em;
  line-height: 1.2em;
  color: #96588a;
}

.pbc-setup-content p {
  font-size: 1em;
  line-height: 1.75em;
  color: #666;
  margin: 0 0 1.75em 0;
}

.pbc-setup-content table {
  border: 0;
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  margin: 0 0 24px 0;
}

.pbc-setup-content table th {
  width: 25%;
  vertical-align: top;
  padding: 12px 0;
  line-height: 1.4em;
  font-weight: 600;
}

.pbc-setup-content table td {
  vertical-align: top;
  padding: 12px 0;
  line-height: 1.4em;
}

.pbc-setup-content table td input,
.pbc-setup-content table td select {
  width: 100% !important;
  max-width: 400px !important;
  padding: 12px 16px !important;
  border: 2px solid #e9ecef !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  transition: all 0.3s ease !important;
  background: #fff !important;
  box-shadow: none !important;
}

.pbc-setup-content table td input:focus,
.pbc-setup-content table td select:focus {
  outline: none !important;
  border-color: #96588a !important;
  box-shadow: 0 0 0 3px rgba(150, 88, 138, 0.1) !important;
}

.pbc-setup-content table td input:hover,
.pbc-setup-content table td select:hover {
  border-color: #dee2e6 !important;
}

.pbc-setup-content .form-table th {
  padding-left: 0;
  padding-right: 24px;
}

.pbc-setup-actions {
  overflow: hidden;
  line-height: 1em;
  margin: 0;
  padding: 0;
  text-align: right;
}

.pbc-setup-actions .button {
  margin-left: 0.5em;
  margin-right: 0;
  margin-bottom: 0;
}

.pbc-setup-actions .button-primary {
  background: linear-gradient(135deg, #96588a 0%, #a16696 100%) !important;
  border-color: #96588a !important;
  border-radius: 6px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 0.5px !important;
  transition: all 0.3s ease !important;
  -webkit-box-shadow: 0 4px 15px rgba(150, 88, 138, 0.3) !important;
  box-shadow: 0 4px 15px rgba(150, 88, 138, 0.3) !important;
  color: #fff !important;
}

.pbc-setup-actions .button-primary:hover,
.pbc-setup-actions .button-primary:focus {
  background: linear-gradient(135deg, #a16696 0%, #96588a 100%) !important;
  border-color: #a16696 !important;
  transform: translateY(-2px) !important;
  -webkit-box-shadow: 0 6px 20px rgba(150, 88, 138, 0.4) !important;
  box-shadow: 0 6px 20px rgba(150, 88, 138, 0.4) !important;
  color: #fff !important;
}

.pbc-setup-actions .button {
  border-radius: 6px !important;
  padding: 12px 24px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.pbc-setup-actions .button:not(.button-primary) {
  background: #f8f9fa !important;
  border-color: #dee2e6 !important;
  color: #6c757d !important;
}

.pbc-setup-actions .button:not(.button-primary):hover {
  background: #e9ecef !important;
  border-color: #adb5bd !important;
  transform: translateY(-1px) !important;
}

.pbc-return-to-dashboard {
  margin: 0;
  padding: 0;
  color: #a16696;
  text-decoration: none;
  line-height: 54px;
}

.pbc-return-to-dashboard:hover,
.pbc-return-to-dashboard:focus {
  color: #96588a;
}

.pbc-sample-preview {
  background: #f9f9f9;
  border: 1px solid #e1e1e1;
  border-radius: 3px;
  padding: 20px;
  margin: 20px 0;
}

.pbc-sample-preview h3 {
  margin-top: 0;
  color: #96588a;
}

.pbc-sample-preview table {
  margin: 0;
}

.pbc-sample-preview table th,
.pbc-sample-preview table td {
  padding: 8px 12px;
  border-bottom: 1px solid #e1e1e1;
}

.pbc-sample-preview table th {
  background: #f1f1f1;
  font-weight: 600;
}

.pbc-setup-next-steps {
  overflow: hidden;
  margin: 0 0 24px 0;
}

.pbc-setup-next-steps-first {
  float: left;
  width: 50%;
  padding-right: 24px;
}

.pbc-setup-next-steps-last {
  float: right;
  width: 50%;
  padding-left: 24px;
}

.pbc-setup-next-steps ul {
  padding: 0;
  margin: 0;
  list-style: none outside;
}

.pbc-setup-next-steps li {
  margin: 0 0 24px 0;
  padding: 0;
  list-style: none outside;
}

.pbc-setup-next-steps h3 {
  margin: 8px 0 4px 0;
  font-size: 1.1em;
}

.pbc-setup-next-steps p {
  margin: 0 0 8px 0;
  font-size: 0.9em;
  line-height: 1.5em;
}

.pbc-setup-next-steps .button {
  margin-bottom: 4px;
}

/* Responsive */
@media screen and (max-width: 782px) {
  .pbc-setup-steps {
    display: block;
  }

  .pbc-setup-steps li {
    display: block;
    width: 100%;
    border-right-width: 4px;
    border-bottom-width: 0;
  }

  .pbc-setup-steps li::before,
  .pbc-setup-steps li::after {
    display: none;
  }

  .pbc-setup-steps li:first-child {
    border-radius: 6px 6px 0 0;
  }

  .pbc-setup-steps li:last-child {
    border-radius: 0 0 6px 6px;
    border-bottom-width: 4px;
  }

  .pbc-setup-content {
    padding: 1em;
  }

  .pbc-setup-content table th,
  .pbc-setup-content table td {
    display: block;
    width: 100%;
    padding: 6px 0;
  }

  .pbc-setup-content .form-table th {
    padding-right: 0;
  }

  .pbc-setup-next-steps-first,
  .pbc-setup-next-steps-last {
    float: none;
    width: 100%;
    padding: 0;
  }

  .pbc-setup-next-steps-last {
    margin-top: 24px;
  }
}

/* Additional modern styling */
.pbc-setup-content .notice {
  background: #f8f9fa;
  border-left: 4px solid #96588a;
  padding: 16px 20px;
  margin: 20px 0;
  border-radius: 0 6px 6px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.pbc-setup-content .notice.notice-success {
  border-left-color: #28a745;
  background: #d4edda;
  color: #155724;
}

.pbc-setup-content .notice.notice-warning {
  border-left-color: #ffc107;
  background: #fff3cd;
  color: #856404;
}

.pbc-setup-content .notice.notice-error {
  border-left-color: #dc3545;
  background: #f8d7da;
  color: #721c24;
}

/* Loading states */
.pbc-setup-loading {
  opacity: 0.6;
  pointer-events: none;
}

.pbc-setup-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #96588a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Improved typography */
.pbc-setup-content .description {
  font-size: 14px;
  color: #6c757d;
  font-style: italic;
  margin-top: 8px;
}

.pbc-setup-content .highlight {
  background: linear-gradient(
    120deg,
    rgba(150, 88, 138, 0.1) 0%,
    rgba(161, 102, 150, 0.1) 100%
  );
  padding: 2px 6px;
  border-radius: 4px;
  font-weight: 500;
}

@media screen and (max-width: 480px) {
  .pbc-setup-actions {
    text-align: center;
  }

  .pbc-setup-actions .button {
    display: block;
    width: 100%;
    margin: 0 0 1em 0;
  }
}

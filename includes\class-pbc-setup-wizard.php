<?php
/**
 * Setup Wizard for Price by Country
 *
 * @package PriceByCountry
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * PBC Setup Wizard Class
 */
class PBC_Setup_Wizard {

    /**
     * Current step
     *
     * @var string
     */
    private $step = '';

    /**
     * Steps for the setup wizard
     *
     * @var array
     */
    private $steps = array();

    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', array($this, 'admin_menus'));
        add_action('admin_init', array($this, 'setup_wizard'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
        
        $this->init_steps();
    }

    /**
     * Initialize wizard steps
     */
    private function init_steps() {
        // Use lazy loading for step names to avoid early translation calls
        $this->steps = array(
            'welcome' => array(
                'name' => $this->get_step_name('Welcome'),
                'view' => array($this, 'setup_welcome'),
                'handler' => array($this, 'setup_welcome_save')
            ),
            'country_detection' => array(
                'name' => $this->get_step_name('Country Detection'),
                'view' => array($this, 'setup_country_detection'),
                'handler' => array($this, 'setup_country_detection_save')
            ),
            'sample_rules' => array(
                'name' => $this->get_step_name('Sample Rules'),
                'view' => array($this, 'setup_sample_rules'),
                'handler' => array($this, 'setup_sample_rules_save')
            ),
            'ready' => array(
                'name' => $this->get_step_name('Ready!'),
                'view' => array($this, 'setup_ready'),
                'handler' => ''
            )
        );
    }

    /**
     * Get translated step name with fallback
     *
     * @param string $name Step name
     * @return string Translated name or fallback
     */
    private function get_step_name($name) {
        if (did_action('init') > 0) {
            return __($name, 'price-by-country');
        }
        return $name; // Return untranslated name as fallback
    }

    /**
     * Add admin menus/screens
     */
    public function admin_menus() {
        // Only show setup wizard menu if setup is not completed
        if (get_option('pbc_setup_completed', false)) {
            return;
        }

        add_submenu_page(
            'woocommerce',
            __('Price by Country Setup Wizard', 'price-by-country'),
            __('Setup Wizard', 'price-by-country'),
            'manage_woocommerce',
            'pbc-setup-wizard',
            array($this, 'render_setup_page')
        );
    }

    /**
     * Render the setup wizard page
     */
    public function render_setup_page() {
        $default_steps = array_keys($this->steps);
        $this->step = isset($_GET['step']) ? sanitize_key($_GET['step']) : current($default_steps);

        // Handle form submissions
        if (!empty($_POST['save_step']) && isset($this->steps[$this->step]['handler']) && !empty($this->steps[$this->step]['handler'])) {
            // Debug logging
            error_log('PBC Setup: Form submitted for step: ' . $this->step);
            error_log('PBC Setup: Posted step: ' . $_POST['save_step']);
            error_log('PBC Setup: Handler exists: ' . (isset($this->steps[$this->step]['handler']) ? 'yes' : 'no'));

            // Verify the step matches the posted step
            if ($_POST['save_step'] === $this->step) {
                error_log('PBC Setup: Calling handler for step: ' . $this->step);
                call_user_func($this->steps[$this->step]['handler'], $this);
                return; // Handler should redirect, so we return here
            } else {
                error_log('PBC Setup: Step mismatch - current: ' . $this->step . ', posted: ' . $_POST['save_step']);
            }
        } else {
            if (!empty($_POST['save_step'])) {
                error_log('PBC Setup: No handler for step: ' . $this->step);
            }
        }

        // Render the admin page
        $this->render_admin_page();
    }

    /**
     * Legacy method for backward compatibility
     */
    public function setup_wizard() {
        // Redirect old setup URLs to new admin page
        if (!empty($_GET['page']) && $_GET['page'] === 'pbc-setup') {
            $redirect_url = admin_url('admin.php?page=pbc-setup-wizard');
            if (!empty($_GET['step'])) {
                $redirect_url = add_query_arg('step', sanitize_key($_GET['step']), $redirect_url);
            }
            wp_redirect($redirect_url);
            exit;
        }
    }

    /**
     * Get the URL for the next step
     *
     * @param string $step Current step
     * @return string Next step URL
     */
    public function get_next_step_link($step = '') {
        if (!$step) {
            $step = $this->step;
        }

        $keys = array_keys($this->steps);
        if (end($keys) === $step) {
            return admin_url();
        }

        $step_index = array_search($step, $keys, true);
        if (false === $step_index) {
            return '';
        }

        return add_query_arg('step', $keys[$step_index + 1], admin_url('admin.php?page=pbc-setup-wizard'));
    }

    /**
     * Render the main admin page
     */
    public function render_admin_page() {
        ?>
        <div class="wrap pbc-setup-wizard">
            <h1 class="wp-heading-inline">
                <span class="dashicons dashicons-admin-settings"></span>
                <?php _e('Price by Country Setup Wizard', 'price-by-country'); ?>
            </h1>

            <?php $this->render_progress_indicator(); ?>

            <div class="pbc-setup-content">
                <div class="pbc-setup-main">
                    <?php $this->render_step_content(); ?>
                </div>
                <div class="pbc-setup-sidebar">
                    <?php $this->render_help_sidebar(); ?>
                </div>
            </div>
        </div>
        <?php
    }

    /**
     * Render progress indicator
     */
    public function render_progress_indicator() {
        $steps = array_keys($this->steps);
        $current_step_index = array_search($this->step, $steps);
        $total_steps = count($steps);
        $progress_percentage = (($current_step_index + 1) / $total_steps) * 100;

        ?>
        <div class="pbc-progress-wrapper">
            <div class="pbc-progress-bar">
                <div class="pbc-progress-fill" style="width: <?php echo esc_attr($progress_percentage); ?>%"></div>
            </div>
            <div class="pbc-progress-steps">
                <?php foreach ($this->steps as $step_key => $step_data): ?>
                    <?php
                    $step_index = array_search($step_key, $steps);
                    $is_current = ($step_key === $this->step);
                    $is_completed = ($step_index < $current_step_index);
                    $step_class = $is_current ? 'current' : ($is_completed ? 'completed' : 'pending');
                    ?>
                    <div class="pbc-progress-step <?php echo esc_attr($step_class); ?>">
                        <span class="step-number"><?php echo esc_html($step_index + 1); ?></span>
                        <span class="step-name"><?php echo esc_html($step_data['name']); ?></span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render step content
     */
    public function render_step_content() {
        if (!isset($this->steps[$this->step])) {
            return;
        }

        $step = $this->steps[$this->step];
        ?>
        <div class="card pbc-setup-step-card">
            <div class="pbc-step-content">
                <?php
                if (isset($step['view']) && is_callable($step['view'])) {
                    call_user_func($step['view']);
                }
                ?>
            </div>
        </div>
        <?php
    }

    /**
     * Render help sidebar
     */
    public function render_help_sidebar() {
        ?>
        <div class="card pbc-setup-help">
            <h3><?php _e('Need Help?', 'price-by-country'); ?></h3>
            <p><?php _e('This setup wizard will guide you through configuring your country-based pricing system.', 'price-by-country'); ?></p>
            <ul>
                <li><a href="https://orbitaddons.com/docs/price-by-country/" target="_blank"><?php _e('Documentation', 'price-by-country'); ?></a></li>
                <li><a href="https://orbitaddons.com/support/" target="_blank"><?php _e('Support', 'price-by-country'); ?></a></li>
                <li><a href="https://orbitaddons.com/contact/" target="_blank"><?php _e('Contact Us', 'price-by-country'); ?></a></li>
            </ul>
        </div>
        <?php
    }

    /**
     * Setup wizard header (deprecated - kept for compatibility)
     */
    public function setup_wizard_header() {
        // Set up admin context properly
        set_current_screen('dashboard');

        ?>
        <!DOCTYPE html>
        <html <?php language_attributes(); ?>>
        <head>
            <meta name="viewport" content="width=device-width" />
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
            <title><?php _e('Price by Country &rsaquo; Setup Wizard', 'price-by-country'); ?></title>
            <?php
            // Only enqueue essential scripts - avoid all WordPress admin styles to prevent emoji deprecation
            wp_enqueue_script('jquery');
            wp_print_scripts();
            ?>
        </head>
        <body class="pbc-setup wp-core-ui wp-admin">
            <div class="pbc-setup-container">
                <h1 id="pbc-logo">
                    <a href="https://orbitaddons.com">
                        <?php _e('Price by Country for WooCommerce', 'price-by-country'); ?>
                    </a>
                </h1>
        <?php
    }

    /**
     * Setup wizard footer
     */
    public function setup_wizard_footer() {
        ?>
            <?php if ('welcome' === $this->step) : ?>
                <a class="pbc-return-to-dashboard" href="<?php echo esc_url(admin_url()); ?>">
                    <?php _e('Not right now', 'price-by-country'); ?>
                </a>
            <?php endif; ?>
            </div> <!-- .pbc-setup-container -->
            </body>
        </html>
        <?php
    }

    /**
     * Output the steps
     */
    public function setup_wizard_steps() {
        $output_steps = $this->steps;
        ?>
        <ol class="pbc-setup-steps">
            <?php
            foreach ($output_steps as $step_key => $step) {
                $is_completed = array_search($this->step, array_keys($this->steps), true) > array_search($step_key, array_keys($this->steps), true);

                if ($step_key === $this->step) {
                    $class = 'active';
                } elseif ($is_completed) {
                    $class = 'done';
                } else {
                    $class = '';
                }
                ?>
                <li class="<?php echo esc_attr($class); ?>">
                    <?php echo esc_html($step['name']); ?>
                </li>
                <?php
            }
            ?>
        </ol>
        <?php
    }

    /**
     * Output the content for the current step
     */
    public function setup_wizard_content() {
        echo '<div class="pbc-setup-content">';
        if (!empty($this->steps[$this->step]['view'])) {
            call_user_func($this->steps[$this->step]['view'], $this);
        }
        echo '</div>';
    }

    /**
     * Welcome step
     */
    public function setup_welcome() {
        ?>
        <h1><?php _e('Welcome to Price by Country for WooCommerce!', 'price-by-country'); ?></h1>
        <p><?php _e('Thank you for choosing Price by Country for WooCommerce! This quick setup wizard will help you configure the basic settings and get you started with country-based pricing.', 'price-by-country'); ?></p>
        <p><?php _e('It should only take a few minutes.', 'price-by-country'); ?></p>
        <p class="pbc-setup-actions step">
            <a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button-primary button button-large button-next">
                <?php _e('Let\'s go!', 'price-by-country'); ?>
            </a>
            <a href="<?php echo esc_url(admin_url()); ?>" class="button button-large">
                <?php _e('Not right now', 'price-by-country'); ?>
            </a>
        </p>
        <?php
    }

    /**
     * Welcome step save
     */
    public function setup_welcome_save() {
        check_admin_referer('pbc-setup');
        wp_redirect(esc_url_raw($this->get_next_step_link()));
        exit;
    }

    /**
     * Country detection step
     */
    public function setup_country_detection() {
        $settings = get_option('pbc_settings', array());
        $detection_method = isset($settings['country_detection_method']) ? $settings['country_detection_method'] : 'auto';
        $detection_priority = isset($settings['detection_priority']) ? $settings['detection_priority'] : array('shipping', 'billing', 'ip');
        ?>
        <h1><?php _e('Country Detection Settings', 'price-by-country'); ?></h1>
        <p><?php _e('Configure how the plugin detects your customers\' countries to show the correct prices.', 'price-by-country'); ?></p>

        <form method="post" action="<?php echo esc_url(admin_url('admin.php?page=pbc-setup-wizard&step=' . $this->step)); ?>">
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="detection_method"><?php _e('Detection Method', 'price-by-country'); ?></label>
                    </th>
                    <td>
                        <select name="detection_method" id="detection_method" class="regular-text">
                            <option value="auto" <?php selected($detection_method, 'auto'); ?>>
                                <?php _e('Automatic (Recommended)', 'price-by-country'); ?>
                            </option>
                            <option value="ip" <?php selected($detection_method, 'ip'); ?>>
                                <?php _e('IP Address Only', 'price-by-country'); ?>
                            </option>
                            <option value="billing" <?php selected($detection_method, 'billing'); ?>>
                                <?php _e('Billing Address Only', 'price-by-country'); ?>
                            </option>
                            <option value="shipping" <?php selected($detection_method, 'shipping'); ?>>
                                <?php _e('Shipping Address Only', 'price-by-country'); ?>
                            </option>
                        </select>
                        <p class="description">
                            <?php _e('Automatic mode uses shipping address first, then billing address, then IP address as fallback.', 'price-by-country'); ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="default_country"><?php _e('Default Country', 'price-by-country'); ?></label>
                    </th>
                    <td>
                        <?php
                        $countries = WC()->countries->get_countries();
                        $default_country = isset($settings['default_country']) ? $settings['default_country'] : substr(get_option('woocommerce_default_country', 'US:*'), 0, 2);
                        ?>
                        <select name="default_country" id="default_country" class="regular-text">
                            <?php foreach ($countries as $code => $name) : ?>
                                <option value="<?php echo esc_attr($code); ?>" <?php selected($default_country, $code); ?>>
                                    <?php echo esc_html($name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description">
                            <?php _e('This country will be used when detection fails or for visitors from countries without specific pricing rules.', 'price-by-country'); ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="cache_duration"><?php _e('Cache Duration', 'price-by-country'); ?></label>
                    </th>
                    <td>
                        <?php $cache_duration = isset($settings['cache_duration']) ? $settings['cache_duration'] : 3600; ?>
                        <select name="cache_duration" id="cache_duration" class="regular-text">
                            <option value="1800" <?php selected($cache_duration, 1800); ?>>
                                <?php _e('30 minutes', 'price-by-country'); ?>
                            </option>
                            <option value="3600" <?php selected($cache_duration, 3600); ?>>
                                <?php _e('1 hour (Recommended)', 'price-by-country'); ?>
                            </option>
                            <option value="7200" <?php selected($cache_duration, 7200); ?>>
                                <?php _e('2 hours', 'price-by-country'); ?>
                            </option>
                            <option value="86400" <?php selected($cache_duration, 86400); ?>>
                                <?php _e('24 hours', 'price-by-country'); ?>
                            </option>
                        </select>
                        <p class="description">
                            <?php _e('How long to cache country detection results for better performance.', 'price-by-country'); ?>
                        </p>
                    </td>
                </tr>
            </table>

            <p class="pbc-setup-actions step">
                <button type="submit" class="button-primary button button-large button-next" name="save_step" value="<?php echo esc_attr($this->step); ?>">
                    <?php _e('Continue', 'price-by-country'); ?>
                </button>
                <a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button button-large button-next">
                    <?php _e('Skip this step', 'price-by-country'); ?>
                </a>
            </p>
            <?php wp_nonce_field('pbc-setup'); ?>
        </form>
        <?php
    }

    /**
     * Country detection step save
     */
    public function setup_country_detection_save() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['_wpnonce'], 'pbc-setup')) {
            wp_die(__('Security check failed. Please try again.', 'price-by-country'));
        }

        // Verify required fields
        if (empty($_POST['detection_method']) || empty($_POST['default_country'])) {
            wp_die(__('Required fields are missing. Please fill in all required fields.', 'price-by-country'));
        }

        $settings = get_option('pbc_settings', array());

        $settings['country_detection_method'] = sanitize_text_field($_POST['detection_method']);
        $settings['default_country'] = sanitize_text_field($_POST['default_country']);
        $settings['cache_duration'] = isset($_POST['cache_duration']) ? absint($_POST['cache_duration']) : 3600;

        update_option('pbc_settings', $settings);

        // Get next step URL
        $next_url = $this->get_next_step_link();

        // Debug: Log the next URL
        error_log('PBC Setup: Next step URL: ' . $next_url);

        wp_redirect(esc_url_raw($next_url));
        exit;
    }

    /**
     * Sample rules step
     */
    public function setup_sample_rules() {
        ?>
        <h1><?php _e('Sample Pricing Rules', 'price-by-country'); ?></h1>
        <p><?php _e('Would you like to create some sample pricing rules to get started? These can be modified or deleted later.', 'price-by-country'); ?></p>

        <form method="post" action="<?php echo esc_url(admin_url('admin.php?page=pbc-setup-wizard&step=' . $this->step)); ?>">
            <table class="form-table">
                <tr>
                    <th scope="row">
                        <label for="create_samples"><?php _e('Create Sample Rules', 'price-by-country'); ?></label>
                    </th>
                    <td>
                        <label>
                            <input type="checkbox" name="create_samples" id="create_samples" value="1" checked>
                            <?php _e('Yes, create sample pricing rules', 'price-by-country'); ?>
                        </label>
                        <p class="description">
                            <?php _e('This will create sample global pricing rules for common countries (US, UK, EU, Canada, Australia).', 'price-by-country'); ?>
                        </p>
                    </td>
                </tr>
                <tr>
                    <th scope="row">
                        <label for="sample_type"><?php _e('Sample Rule Type', 'price-by-country'); ?></label>
                    </th>
                    <td>
                        <select name="sample_type" id="sample_type" class="regular-text">
                            <option value="percentage"><?php _e('Percentage-based adjustments', 'price-by-country'); ?></option>
                            <option value="fixed"><?php _e('Fixed amount adjustments', 'price-by-country'); ?></option>
                        </select>
                        <p class="description">
                            <?php _e('Choose whether sample rules should use percentage or fixed amount adjustments.', 'price-by-country'); ?>
                        </p>
                    </td>
                </tr>
            </table>

            <div class="pbc-sample-preview">
                <h3><?php _e('Sample Rules Preview', 'price-by-country'); ?></h3>
                <table class="widefat">
                    <thead>
                        <tr>
                            <th><?php _e('Country', 'price-by-country'); ?></th>
                            <th><?php _e('Adjustment', 'price-by-country'); ?></th>
                            <th><?php _e('Example (Base Price: $100)', 'price-by-country'); ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><?php _e('United States', 'price-by-country'); ?></td>
                            <td>0% (Base Price)</td>
                            <td>$100.00</td>
                        </tr>
                        <tr>
                            <td><?php _e('United Kingdom', 'price-by-country'); ?></td>
                            <td>+15%</td>
                            <td>$115.00</td>
                        </tr>
                        <tr>
                            <td><?php _e('European Union', 'price-by-country'); ?></td>
                            <td>+10%</td>
                            <td>$110.00</td>
                        </tr>
                        <tr>
                            <td><?php _e('Canada', 'price-by-country'); ?></td>
                            <td>-5%</td>
                            <td>$95.00</td>
                        </tr>
                        <tr>
                            <td><?php _e('Australia', 'price-by-country'); ?></td>
                            <td>+20%</td>
                            <td>$120.00</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <p class="pbc-setup-actions step">
                <button type="submit" class="button-primary button button-large button-next" name="save_step" value="<?php echo esc_attr($this->step); ?>">
                    <?php _e('Continue', 'price-by-country'); ?>
                </button>
                <a href="<?php echo esc_url($this->get_next_step_link()); ?>" class="button button-large button-next">
                    <?php _e('Skip this step', 'price-by-country'); ?>
                </a>
            </p>
            <?php wp_nonce_field('pbc-setup'); ?>
        </form>
        <?php
    }

    /**
     * Sample rules step save
     */
    public function setup_sample_rules_save() {
        check_admin_referer('pbc-setup');

        if (!empty($_POST['create_samples'])) {
            $this->create_sample_rules($_POST['sample_type']);
        }

        wp_redirect(esc_url_raw($this->get_next_step_link()));
        exit;
    }

    /**
     * Create sample pricing rules
     *
     * @param string $type Rule type (percentage or fixed)
     */
    private function create_sample_rules($type = 'percentage') {
        $core = PBC_Core::get_instance();
        $database = $core->database;

        $sample_rules = array();

        if ($type === 'percentage') {
            $sample_rules = array(
                array('country_code' => 'GB', 'adjustment_type' => 'percentage', 'adjustment_value' => 15),
                array('country_code' => 'DE', 'adjustment_type' => 'percentage', 'adjustment_value' => 10),
                array('country_code' => 'FR', 'adjustment_type' => 'percentage', 'adjustment_value' => 10),
                array('country_code' => 'CA', 'adjustment_type' => 'percentage', 'adjustment_value' => -5),
                array('country_code' => 'AU', 'adjustment_type' => 'percentage', 'adjustment_value' => 20),
            );
        } else {
            $sample_rules = array(
                array('country_code' => 'GB', 'adjustment_type' => 'fixed', 'adjustment_value' => 15),
                array('country_code' => 'DE', 'adjustment_type' => 'fixed', 'adjustment_value' => 10),
                array('country_code' => 'FR', 'adjustment_type' => 'fixed', 'adjustment_value' => 10),
                array('country_code' => 'CA', 'adjustment_type' => 'fixed', 'adjustment_value' => -5),
                array('country_code' => 'AU', 'adjustment_type' => 'fixed', 'adjustment_value' => 20),
            );
        }

        foreach ($sample_rules as $rule) {
            $rule_data = array(
                'rule_type' => 'global',
                'object_id' => null,
                'country_code' => $rule['country_code'],
                'adjustment_type' => $rule['adjustment_type'],
                'adjustment_value' => $rule['adjustment_value'],
                'is_active' => 1
            );

            $database->save_pricing_rule($rule_data);
        }
    }

    /**
     * Final step
     */
    public function setup_ready() {
        ?>
        <h1><?php _e('Your store is ready!', 'price-by-country'); ?></h1>

        <div class="pbc-setup-next-steps">
            <div class="pbc-setup-next-steps-first">
                <h2><?php _e('Next steps', 'price-by-country'); ?></h2>
                <ul>
                    <li class="setup-product">
                        <a class="button button-primary button-large" href="<?php echo esc_url(admin_url('admin.php?page=wc-settings&tab=pbc_settings')); ?>">
                            <?php _e('Configure Settings', 'price-by-country'); ?>
                        </a>
                        <h3><?php _e('Configure your pricing settings', 'price-by-country'); ?></h3>
                        <p><?php _e('Visit the settings page to fine-tune your country detection and pricing options.', 'price-by-country'); ?></p>
                    </li>
                    <li class="setup-woocommerce">
                        <a class="button button-large" href="<?php echo esc_url(admin_url('edit.php?post_type=product')); ?>">
                            <?php _e('Add Product Pricing', 'price-by-country'); ?>
                        </a>
                        <h3><?php _e('Set up product-specific pricing', 'price-by-country'); ?></h3>
                        <p><?php _e('Edit your products to add country-specific pricing rules.', 'price-by-country'); ?></p>
                    </li>
                    <li class="setup-customize">
                        <a class="button button-large" href="<?php echo esc_url(admin_url('admin.php?page=pbc_dashboard')); ?>">
                            <?php _e('View Dashboard', 'price-by-country'); ?>
                        </a>
                        <h3><?php _e('Monitor your pricing rules', 'price-by-country'); ?></h3>
                        <p><?php _e('Use the dashboard to manage and monitor all your pricing rules.', 'price-by-country'); ?></p>
                    </li>
                </ul>
            </div>
            <div class="pbc-setup-next-steps-last">
                <h2><?php _e('Learn more', 'price-by-country'); ?></h2>
                <ul>
                    <li>
                        <a href="https://orbitaddons.com/docs/price-by-country/" target="_blank">
                            <?php _e('Read the documentation', 'price-by-country'); ?>
                        </a>
                    </li>
                    <li>
                        <a href="https://orbitaddons.com/support/" target="_blank">
                            <?php _e('Get support', 'price-by-country'); ?>
                        </a>
                    </li>
                    <li>
                        <a href="https://orbitaddons.com/price-by-country/examples/" target="_blank">
                            <?php _e('View examples', 'price-by-country'); ?>
                        </a>
                    </li>
                </ul>
            </div>
        </div>

        <p class="pbc-setup-actions step">
            <a class="button button-primary button-large" href="<?php echo esc_url(admin_url()); ?>">
                <?php _e('Go to Dashboard', 'price-by-country'); ?>
            </a>
        </p>
        <?php

        // Mark setup as completed
        update_option('pbc_setup_completed', true);
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Check for both old and new page slugs
        $is_setup_page = (!empty($_GET['page']) && ($_GET['page'] === 'pbc-setup-wizard' || $_GET['page'] === 'pbc-setup'));

        if (!$is_setup_page) {
            return;
        }

        // Enqueue WordPress admin styles
        wp_enqueue_style('wp-admin');
        wp_enqueue_style('common');
        wp_enqueue_style('forms');

        // Enqueue our setup-specific styles and scripts
        wp_enqueue_style('pbc-setup', PBC_PLUGIN_URL . 'admin/css/pbc-setup.css', array('dashicons', 'wp-admin', 'common'), PBC_VERSION);
        wp_enqueue_script('pbc-setup', PBC_PLUGIN_URL . 'admin/js/pbc-setup.js', array('jquery'), PBC_VERSION);
    }

    /**
     * Check if setup wizard should be shown
     *
     * @return bool
     */
    public static function should_show_setup() {
        return !get_option('pbc_setup_completed', false) && current_user_can('manage_woocommerce');
    }

    /**
     * Get setup wizard URL
     *
     * @return string
     */
    public static function get_setup_url() {
        return admin_url('admin.php?page=pbc-setup-wizard');
    }
}